"use client";

import { useState } from "react";
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Search,
  ArrowRight,
  BookOpen,
  Calculator,
  Building,
  CreditCard,
  TrendingUp,
  HelpCircle,
  Star
} from "lucide-react";
import { FAQItem, FAQCategory, searchFAQs } from "@/lib/faq";

interface FAQPageProps {
  dict: any;
  lang: 'en' | 'fr' | 'ar';
  faqs: FAQItem[];
  categories: FAQCategory[];
  featuredFAQs: FAQItem[];
}

const iconMap = {
  BookOpen,
  Calculator,
  Building,
  CreditCard,
  TrendingUp,
  HelpCircle
};

export function FAQPage({ dict, lang, faqs, categories, featuredFAQs }: FAQPageProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredFAQs, setFilteredFAQs] = useState(faqs);
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [isSearching, setIsSearching] = useState(false);

  // Generate FAQ structured data for SEO
  const faqStructuredData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer.replace(/\*\*(.*?)\*\*/g, '$1').replace(/\*(.*?)\*/g, '$1')
      }
    }))
  };

  const handleSearch = async (term: string) => {
    setSearchTerm(term);
    setIsSearching(true);
    
    if (!term) {
      setFilteredFAQs(selectedCategory === "all" ? faqs : faqs.filter(faq => faq.category === selectedCategory));
      setIsSearching(false);
      return;
    }
    
    try {
      const searchResults = await searchFAQs(term, lang);
      setFilteredFAQs(searchResults);
    } catch (error) {
      console.error('Search error:', error);
      setFilteredFAQs([]);
    } finally {
      setIsSearching(false);
    }
  };

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    if (searchTerm) {
      handleSearch(searchTerm);
    } else {
      setFilteredFAQs(category === "all" ? faqs : faqs.filter(faq => faq.category === category));
    }
  };

  const formatAnswer = (answer: string) => {
    return answer
      // Format main headings (##)
      .replace(/^## (.*$)/gm, '<h3 class="text-xl font-bold text-primary mt-6 mb-4 border-b border-border pb-2">$1</h3>')
      // Format sub-headings (###)
      .replace(/^### (.*$)/gm, '<h4 class="text-lg font-semibold text-foreground mt-5 mb-3">$1</h4>')
      // Format bold text
      .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-foreground">$1</strong>')
      // Format italic text
      .replace(/\*(.*?)\*/g, '<em class="italic text-muted-foreground">$1</em>')
      // Format inline code
      .replace(/`(.*?)`/g, '<code class="bg-muted px-2 py-1 rounded text-sm font-mono">$1</code>')
      // Format code blocks
      .replace(/```([\s\S]*?)```/g, '<pre class="bg-muted p-4 rounded-lg overflow-x-auto my-4"><code class="text-sm font-mono">$1</code></pre>')
      // Split into paragraphs and format
      .split('\n\n')
      .map(paragraph => {
        // Skip if already formatted as heading or code block
        if (paragraph.includes('<h3') || paragraph.includes('<h4') || paragraph.includes('<pre') || paragraph.includes('<table')) {
          return paragraph;
        }

        // Handle lists
        if (paragraph.includes('- **') || paragraph.includes('- ')) {
          const listItems = paragraph.split('\n').map(line => {
            if (line.trim().startsWith('- ')) {
              return `<li class="mb-2">${line.replace(/^- /, '')}</li>`;
            }
            return line;
          }).join('\n');
          return `<ul class="list-disc list-inside space-y-2 mb-4 text-muted-foreground">${listItems}</ul>`;
        }

        // Handle tables
        if (paragraph.includes('|')) {
          const lines = paragraph.split('\n');
          if (lines.length >= 2 && lines[1].includes('---')) {
            const headers = lines[0].split('|').map(h => h.trim()).filter(h => h);
            const rows = lines.slice(2).map(line =>
              line.split('|').map(cell => cell.trim()).filter(cell => cell)
            );

            let tableHtml = '<div class="overflow-x-auto my-4"><table class="min-w-full border border-border rounded-lg">';
            tableHtml += '<thead class="bg-muted"><tr>';
            headers.forEach(header => {
              tableHtml += `<th class="px-4 py-2 text-left font-semibold border-b border-border">${header}</th>`;
            });
            tableHtml += '</tr></thead><tbody>';

            rows.forEach((row, index) => {
              tableHtml += `<tr class="${index % 2 === 0 ? 'bg-background' : 'bg-muted/50'}">`;
              row.forEach(cell => {
                tableHtml += `<td class="px-4 py-2 border-b border-border text-sm">${cell}</td>`;
              });
              tableHtml += '</tr>';
            });

            tableHtml += '</tbody></table></div>';
            return tableHtml;
          }
        }

        // Regular paragraphs
        if (paragraph.trim()) {
          return `<p class="mb-4 leading-relaxed text-muted-foreground">${paragraph}</p>`;
        }

        return paragraph;
      })
      .join('\n');
  };

  return (
    <>
      {/* FAQ Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(faqStructuredData) }}
      />

      <div className={`flex min-h-screen w-full flex-col bg-background pt-16 ${lang === 'ar' ? 'rtl' : 'ltr'}`}>

      <div className="w-full max-w-6xl mx-auto px-4 sm:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <Link href={`/${lang}`} className="inline-flex items-center text-primary hover:text-primary/80 mb-4">
            <ArrowRight className={`h-4 w-4 ${lang === 'ar' ? 'ml-2' : 'mr-2 rotate-180'}`} />
            {dict.navigation.home}
          </Link>
          <h1 className="text-4xl sm:text-5xl font-bold text-primary tracking-tight mb-4">
            {dict.faq.title}
          </h1>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            {dict.faq.description}
          </p>
        </div>

        {/* Search */}
        <div className="max-w-md mx-auto mb-8">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder={dict.faq.searchPlaceholder}
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Quick Navigation */}
        {!searchTerm && featuredFAQs.length > 0 && (
          <div className="mb-12 p-4 bg-muted/30 rounded-lg border">
            <h3 className="text-sm font-semibold mb-3 text-muted-foreground uppercase tracking-wide">
              {dict.faq.quickNavigation || 'Quick Navigation'}
            </h3>
            <div className="flex flex-wrap gap-2">
              {featuredFAQs.map((faq) => (
                <Button
                  key={faq.id}
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const element = document.getElementById(`faq-${faq.id}`);
                    element?.scrollIntoView({ behavior: 'smooth' });
                  }}
                  className="text-xs h-8"
                >
                  {faq.question.length > 35 ? faq.question.substring(0, 35) + '...' : faq.question}
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* Featured FAQs */}
        {!searchTerm && featuredFAQs.length > 0 && (
          <section className="mb-16">
            <div className="flex items-center gap-2 mb-8">
              <Star className="h-5 w-5 text-yellow-500" />
              <h2 className="text-2xl font-bold">{dict.faq.featuredQuestions}</h2>
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              {featuredFAQs.map((faq) => (
                <Card key={faq.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <CardTitle className="text-lg">{faq.question}</CardTitle>
                    <div className="flex flex-wrap gap-2">
                      {faq.tags.slice(0, 3).map((tag) => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div 
                      className="text-muted-foreground line-clamp-3 mb-4"
                      dangerouslySetInnerHTML={{ 
                        __html: formatAnswer(faq.answer.split('\n\n')[0]) 
                      }}
                    />
                    <Button variant="ghost" size="sm" onClick={() => {
                      const element = document.getElementById(`faq-${faq.id}`);
                      element?.scrollIntoView({ behavior: 'smooth' });
                    }}>
                      {dict.faq.readFullAnswer}
                      <ArrowRight className={`h-4 w-4 ${lang === 'ar' ? 'mr-1 rotate-180' : 'ml-1'}`} />
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>
        )}

        {/* Categories and FAQs */}
        <Tabs value={selectedCategory} onValueChange={handleCategoryChange} className="w-full">
          <TabsList className="grid w-full grid-cols-3 lg:grid-cols-6 mb-8">
            <TabsTrigger value="all" className="flex items-center gap-2">
              <HelpCircle className="h-4 w-4" />
              <span className="hidden sm:inline">{dict.faq.allCategories}</span>
              <span className="sm:hidden">All</span>
            </TabsTrigger>
            {categories.map((category) => {
              const IconComponent = iconMap[category.icon as keyof typeof iconMap] || HelpCircle;
              return (
                <TabsTrigger key={category.id} value={category.id} className="flex items-center gap-2">
                  <IconComponent className="h-4 w-4" />
                  <span className="hidden lg:inline">{category.name}</span>
                  <span className="lg:hidden">{category.name.split(' ')[0]}</span>
                </TabsTrigger>
              );
            })}
          </TabsList>

          <TabsContent value="all" className="mt-0">
            <FAQList faqs={filteredFAQs} dict={dict} formatAnswer={formatAnswer} isSearching={isSearching} lang={lang} />
          </TabsContent>

          {categories.map((category) => (
            <TabsContent key={category.id} value={category.id} className="mt-0">
              <div className="mb-6">
                <h3 className="text-xl font-semibold mb-2">{category.name}</h3>
                <p className="text-muted-foreground">{category.description}</p>
              </div>
              <FAQList
                faqs={filteredFAQs.filter(faq => faq.category === category.id)}
                dict={dict}
                formatAnswer={formatAnswer}
                isSearching={isSearching}
                lang={lang}
              />
            </TabsContent>
          ))}
        </Tabs>

        {/* Call to Action */}
        <Card className="mt-16 bg-primary/5 border-primary/20">
          <CardContent className="p-8 text-center">
            <Calculator className="mx-auto h-12 w-12 text-primary mb-4" />
            <h3 className="text-xl font-semibold mb-2">
              {dict.faq.calculatorCTA}
            </h3>
            <p className="text-muted-foreground mb-4">
              {dict.faq.calculatorDescription}
            </p>
            <Button asChild size="lg">
              <Link href={`/${lang}`}>
                {dict.faq.tryCalculator}
                <Calculator className={`h-4 w-4 ${lang === 'ar' ? 'mr-2' : 'ml-2'}`} />
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
    </>
  );
}

interface FAQListProps {
  faqs: FAQItem[];
  dict: any;
  formatAnswer: (answer: string) => string;
  isSearching: boolean;
  lang: 'en' | 'fr' | 'ar';
}

function FAQList({ faqs, dict, formatAnswer, isSearching, lang }: FAQListProps) {
  const [showFullAnswer, setShowFullAnswer] = useState<Set<string>>(new Set());

  const toggleFullAnswer = (faqId: string) => {
    const newSet = new Set(showFullAnswer);
    if (newSet.has(faqId)) {
      newSet.delete(faqId);
    } else {
      newSet.add(faqId);
    }
    setShowFullAnswer(newSet);
  };

  const getPreviewText = (answer: string) => {
    const firstParagraph = answer.split('\n\n')[0];
    return firstParagraph.length > 200 ? firstParagraph.substring(0, 200) + '...' : firstParagraph;
  };

  const isLongAnswer = (answer: string) => {
    return answer.length > 500 || answer.split('\n\n').length > 3;
  };

  if (isSearching) {
    return (
      <div className="text-center py-8">
        <Search className="mx-auto h-8 w-8 text-muted-foreground/50 mb-2 animate-pulse" />
        <p className="text-muted-foreground">{dict.faq.searching}</p>
      </div>
    );
  }

  if (faqs.length === 0) {
    return (
      <div className="text-center py-16">
        <HelpCircle className="mx-auto h-16 w-16 text-muted-foreground/50 mb-4" />
        <h3 className="text-xl font-semibold mb-2">{dict.faq.noResults}</h3>
        <p className="text-muted-foreground">
          {dict.faq.noResultsDescription}
        </p>
      </div>
    );
  }

  return (
    <Accordion type="single" collapsible className="w-full space-y-4">
      {faqs.map((faq) => {
        const isLong = isLongAnswer(faq.answer);
        const showFull = showFullAnswer.has(faq.id);
        const displayAnswer = isLong && !showFull ? getPreviewText(faq.answer) : faq.answer;

        return (
          <AccordionItem
            key={faq.id}
            value={faq.id}
            id={`faq-${faq.id}`}
            className="border rounded-lg px-6 shadow-sm hover:shadow-md transition-shadow"
          >
            <AccordionTrigger className="text-left hover:no-underline py-6">
              <div className="flex items-start gap-4 w-full">
                <div className="flex-1">
                  <h3 className="font-semibold text-lg mb-3 text-foreground">{faq.question}</h3>
                  <div className="flex flex-wrap gap-2">
                    {faq.tags.slice(0, 4).map((tag) => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </AccordionTrigger>
            <AccordionContent className="pt-2 pb-6">
              <div
                className="max-w-none text-sm leading-relaxed"
                dangerouslySetInnerHTML={{ __html: formatAnswer(displayAnswer) }}
              />
              {isLong && (
                <div className="mt-4 pt-4 border-t border-border">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleFullAnswer(faq.id)}
                    className="text-primary hover:text-primary/80"
                  >
                    {showFull ? (dict.faq.showLess || 'Show Less') : (dict.faq.readMore || 'Read More')}
                    <ArrowRight className={`h-4 w-4 ${lang === 'ar' ? 'mr-2' : 'ml-2'} transition-transform ${showFull ? 'rotate-90' : ''}`} />
                  </Button>
                </div>
              )}
            </AccordionContent>
          </AccordionItem>
        );
      })}
    </Accordion>
  );
}
